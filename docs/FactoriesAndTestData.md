# Model Factories and Test Data Generation

This document describes the Laravel model factories and custom Artisan command for generating realistic test data in the multi-tenant application.

## Overview

The system includes comprehensive factories for generating test data and a custom command that creates realistic, related data for development and testing purposes.

## Model Factories

### TenantFactory

**Location:** `database/factories/TenantFactory.php`

Creates realistic tenant (company) data with proper enum values and metadata.

#### Features:
- Generates realistic Polish company data
- Creates proper VAT IDs in Polish format (XXX-XXX-XX-XX)
- Includes complete tenant metadata with bank accounts and accounting data
- Supports various business types and VAT configurations

#### Usage Examples:

```php
// Basic tenant
$tenant = Tenant::factory()->create();

// Tenant with metadata
$tenant = Tenant::factory()->withMetadata()->create();

// Individual business
$tenant = Tenant::factory()->individual()->create();

// Business entity
$tenant = Tenant::factory()->business()->create();

// Inactive tenant
$tenant = Tenant::factory()->inactive()->create();
```

#### Generated Metadata:
- **Accounting Data:** REGON number, BDO identifier
- **Bank Accounts:** Polish bank accounts with realistic bank names
- **Invoice Configuration:** Default template settings
- **30% chance** of additional EUR account

### PartnerFactory

**Location:** `database/factories/PartnerFactory.php`

Creates realistic business partner data with proper relationships to tenants.

#### Features:
- Generates both individual and business partners
- Creates realistic Polish bank account numbers
- Supports EU and non-EU partners with appropriate VAT configurations
- Includes various partner types (suppliers, customers, etc.)

#### Usage Examples:

```php
// Basic partner for a tenant
$partner = Partner::factory()->forTenant($tenant)->create();

// Individual partner
$partner = Partner::factory()->individual()->create();

// Business partner
$partner = Partner::factory()->business()->create();

// EU partner
$partner = Partner::factory()->euPartner()->create();

// Non-EU partner
$partner = Partner::factory()->nonEuPartner()->create();

// Supplier
$partner = Partner::factory()->supplier()->create();

// Customer
$partner = Partner::factory()->customer()->create();
```

### UserFactory (Enhanced)

**Location:** `database/factories/UserFactory.php`

Enhanced existing factory with tenant-specific functionality and role assignment.

#### New Features:
- Tenant admin role assignment
- Polish names and email generation
- Tenant relationship management
- Various user states (active/inactive)
- **Automatic ProfileData creation** with realistic Polish data

#### Usage Examples:

```php
// Tenant admin user
$user = User::factory()->tenantAdmin()->forTenant($tenant)->create();

// Employee user
$user = User::factory()->employee()->create();

// Polish-style user
$user = User::factory()->polish()->create();

// User with custom password
$user = User::factory()->withPassword('custom123')->create();

// User with profile data (automatically created with role methods)
$user = User::factory()->withProfile()->create();
```

## Custom Artisan Command

### app:seed-dummy-data

**Location:** `app/Console/Commands/SeedDummyData.php`

Generates comprehensive test data for the multi-tenant application.

#### Command Signature:
```bash
./server artisan app:seed-dummy-data [options]
```

#### Options:
- `--tenants=5` - Number of tenants to create (default: 5)
- `--min-users=1` - Minimum admin users per tenant (default: 1)
- `--max-users=3` - Maximum admin users per tenant (default: 3)
- `--min-partners=2` - Minimum partners per tenant (default: 2)
- `--max-partners=5` - Maximum partners per tenant (default: 5)
- `--force` - Skip confirmation prompt

#### Usage Examples:

```bash
# Default generation (5 tenants)
./server artisan app:seed-dummy-data

# Custom configuration
./server artisan app:seed-dummy-data --tenants=10 --min-users=2 --max-users=4

# Force execution without confirmation
./server artisan app:seed-dummy-data --tenants=3 --force

# Small test dataset
./server artisan app:seed-dummy-data --tenants=1 --min-users=1 --max-users=1 --min-partners=1 --max-partners=2
```

#### What Gets Created:

For each tenant:
1. **Tenant** with complete metadata including:
   - Accounting data (REGON, BDO)
   - Bank accounts (PLN, optionally EUR)
   - Invoice configuration
2. **Admin Users** (1-3 per tenant):
   - Polish names and emails
   - Tenant Admin role
   - Linked to specific tenant
   - **Complete ProfileData** with name, surname, address, phone
3. **Partners** (2-5 per tenant):
   - Mix of individuals and businesses
   - Various types: suppliers, customers
   - Some EU/non-EU partners (20%/10% chance)
   - Proper VAT configurations

#### Features:
- **Transaction Safety:** All operations wrapped in database transaction
- **Progress Feedback:** Real-time progress bar and status updates
- **Error Handling:** Comprehensive error handling with rollback
- **Validation:** Input validation for all parameters
- **Confirmation:** Interactive confirmation (unless --force used)

## Data Quality

### Realistic Data Generation:
- **Polish Company Names:** Realistic business names
- **Polish Personal Names:** Common Polish first and last names
- **VAT IDs:** Properly formatted Polish NIP numbers
- **Bank Accounts:** Valid Polish bank account format
- **Addresses:** Realistic Polish addresses
- **Phone Numbers:** Polish phone number formats

### Relationships:
- All users properly linked to tenants
- All partners associated with correct tenants
- Proper role assignments (Tenant Admin)
- Metadata correctly structured using DTOTenantMetadata
- **ProfileData automatically created** for all users with complete information

### Enum Usage:
- **Business Types:** Individual vs Business
- **VAT Types:** Local, EU, Non-EU, Not VAT payer
- **Tax Types:** Progressive, Linear, Flat, None
- **Accounting Types:** Full, IOR, IR, None

## Testing the Factories

### Unit Testing:
```php
// Test tenant creation
$tenant = Tenant::factory()->withMetadata()->create();
$this->assertNotNull($tenant->meta);
$this->assertNotNull($tenant->vat_id);

// Test user-tenant relationship
$user = User::factory()->tenantAdmin()->forTenant($tenant)->create();
$this->assertTrue($user->isTenantAdmin());
$this->assertTrue($user->tenant->contains($tenant));

// Test partner creation
$partner = Partner::factory()->forTenant($tenant)->business()->create();
$this->assertEquals($tenant->id, $partner->installation);
```

### Manual Testing:
```bash
# Create test data
./server artisan app:seed-dummy-data --tenants=1 --force

# Verify in Tinker
./server artisan tinker
>>> $tenant = Tenant::latest()->first()
>>> $tenant->meta->meta // Check metadata structure
>>> $tenant->user // Check associated users
>>> $tenant->partners // Check associated partners
```

## Best Practices

1. **Use Transactions:** Always wrap factory usage in transactions for tests
2. **Clean Data:** Use database transactions or refresh database between tests
3. **Realistic Data:** Factories generate realistic data suitable for demos
4. **Relationships:** Always use proper factory methods for relationships
5. **State Methods:** Use factory state methods for specific configurations

## Default Credentials

- **All Users:** Password is `password`
- **Admin Access:** All created users have Tenant Admin role
- **Active Status:** All tenants and users are active by default

This comprehensive factory system provides a reliable way to generate realistic test data for development, testing, and demonstration purposes.
