services:
  app:
    build:
      context: ./docker/app
      dockerfile: fpm.dockerfile
      args:
        WWWGROUP: '${WWWGROUP:-1000}'
        WWWUSER: '${WWWUSER:-1000}'
    image: twojefaktury/app
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    environment:
      WWWUSER: '${WWWUSER:-1000}'
      PHP_MEMORY_LIMIT: '${PHP_MEMORY_LIMIT:-512M}'
      PHP_MAX_EXECUTION_TIME: '${PHP_MAX_EXECUTION_TIME:-600}'
      PHP_MAX_INPUT_TIME: '${PHP_MAX_INPUT_TIME:-600}'
      PHP_UPLOAD_MAX_FILESIZE: '${PHP_UPLOAD_MAX_FILESIZE:-100M}'
      PHP_POST_MAX_SIZE: '${PHP_POST_MAX_SIZE:-100M}'
      PHP_DATE_TIMEZONE: '${PHP_DATE_TIMEZONE:-UTC}'
      PHP_OPCACHE_ENABLE: '${PHP_OPCACHE_ENABLE:-1}'
      PHP_ERROR_LOG: '/var/log/php-fpm/error.log'
      PHP_FPM_PM_CONTROL: 'dynamic'
      PHP_FPM_PM_START_SERVERS: '32'
      PHP_FPM_PM_MAX_CHILDREN: '120'
      PHP_FPM_PM_MAX_SPARE_SERVERS: '48'
      PHP_FPM_PM_MIN_SPARE_SERVERS: '16'
    volumes:
      - '.:/var/www/html'
      - './docker/app/logs:/var/log/php-fpm'
    networks:
      - twojefaktury
    container_name: app
    expose:
      - '9000'
    restart: unless-stopped
    depends_on:
      - redis
      - mariadb
      - nginx
      - queue
      - cron

  nginx:
    build:
      args:
        NGINX_BUILD: '$NGINX_BUILD:-nginx'
      context: .
      dockerfile: docker/nginx/${NGINX_BUILD:-nginx}.dockerfile
    container_name: nginx
    image: twojefaktury/nginx
    restart: unless-stopped
    tty: true
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/${NGINX_CONF:-default.conf}:/etc/nginx/conf.d/default.conf
      - .:/var/www/html:delegated
      - ./docker/nginx/logs:/var/log/nginx
    networks:
      - twojefaktury

  queue:
    image: twojefaktury/app
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    environment:
      WWWUSER: '${WWWUSER:-1000}'
      PHP_FPM_POOL_NAME: 'queue'
      PHP_ERROR_LOG: '/var/log/php/error.log'
      PHP_OPCACHE_ENABLE: '${PHP_OPCACHE_ENABLE:-1}'
      PHP_MEMORY_LIMIT: '${PHP_MEMORY_LIMIT:-512M}'
    volumes:
      - '.:/var/www/html'
      - './docker/queue/logs:/var/log/php'
    networks:
      - twojefaktury
    container_name: queue
    command: [ "php", "/var/www/html/artisan", "queue:work", "--sleep=3", "--tries=3", "--timeout=0", "--memory=512" ]
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "php", "artisan", "app:health" ]
      retries: 3
      timeout: 5s
    depends_on:
      - redis
      - mariadb

  cron:
    container_name: cron
    image: twojefaktury/app
    stop_signal: SIGTERM
    volumes:
      - '.:/var/www/html'
      - './docker/cron/logs:/var/log/php'
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    environment:
      WWWUSER: '${WWWUSER}'
      PHP_FPM_POOL_NAME: 'cron'
      PHP_ERROR_LOG: '/var/log/php/error.log'
      PHP_OPCACHE_ENABLE: '${PHP_OPCACHE_ENABLE:-1}'
      PHP_MEMORY_LIMIT: '${PHP_MEMORY_LIMIT:-1024M}'
    depends_on:
      - redis
      - mariadb
    networks:
      - twojefaktury
    healthcheck:
      test: [ "CMD", "php", "artisan", "app:health" ]
      retries: 3
      timeout: 5s
    command: [ "php", "/var/www/html/artisan", "schedule:work" ]
    restart: unless-stopped

  redis:
    image: 'redis:alpine'
    mem_limit: 512m
    ports:
      - '127.0.0.1:${FORWARD_REDIS_PORT:-6379}:6379'
    volumes:
      - './docker/redis/data:/data'
    networks:
      - twojefaktury
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      retries: 3
      timeout: 5s
    container_name: redis
    restart: unless-stopped

  mariadb:
    image: 'mariadb:11'
    ports:
      - '${FORWARD_DB_PORT:-3306}:3306'
      - '${FORWARD_DB_REPLICA_PORT:-127.0.0.1:3307}:3306'
    environment:
      MYSQL_ROOT_PASSWORD: '${DB_PASSWORD}'
      MYSQL_ROOT_HOST: '%'
      MYSQL_DATABASE: '${DB_DATABASE}'
      MYSQL_USER: '${DB_USERNAME}'
      MYSQL_PASSWORD: '${DB_PASSWORD}'
      MYSQL_ALLOW_EMPTY_PASSWORD: 'yes'
      MARIADB_REPLICATION_USER: '${MARIADB_REPLICATION_USER}'
      MARIADB_REPLICATION_PASSWORD: '${MARIADB_REPLICATION_PASSWORD}'
    volumes:
      - './docker/mysql/data:/var/lib/mysql'
      - './docker/mysql/conf:/etc/mysql/conf.d'
      - './docker/mysql/schema:/schema'
      - './docker/mysql/scripts:/scripts'
      - './docker/mysql/backup:/backup'
    networks:
      - twojefaktury
    container_name: mariadb
    restart: always
    healthcheck:
      test: ["CMD", "healthcheck.sh", "--connect", "--innodb_initialized"]
      retries: 3
      interval: 5s
      start_period: 5s
      timeout: 5s

#  meilisearch:
#    image: 'getmeili/meilisearch:latest'
#    ports:
#      - '127.0.0.1:${FORWARD_MEILISEARCH_PORT:-7700}:7700'
#    environment:
#      MEILI_NO_ANALYTICS: '${MEILISEARCH_NO_ANALYTICS:-false}'
#    volumes:
#      - './docker/meilisearch/data:/meili_data'
#    networks:
#      - twojefaktury
#    container_name: meilisearch
#    restart: unless-stopped
#    profiles:
#      - development
#    depends_on:
#      - app
#    healthcheck:
#      test: [ "CMD", "wget", "--no-verbose", "--spider", "http://127.0.0.1:7700/health" ]
#      retries: 3
#      timeout: 5s


  mailhog_server:
      image: 'mailhog/mailhog:latest'
      logging:
        driver: 'none'
      ports:
        - '1025:1025'
        - '8025:8025'
      networks:
        - twojefaktury
      depends_on:
        - app
      container_name: mailhog
      profiles:
        - development
      restart: unless-stopped

networks:
  twojefaktury:
    driver: bridge
