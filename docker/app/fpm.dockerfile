FROM serversideup/php:8.3-fpm

ARG WWWGROUP
ARG WWWUSER

ENV TZ=UTC
WORKDIR /var/www/html

USER root
RUN apt-get update && apt-get upgrade -y && apt-get install -y --no-install-recommends \
    sqlite3 \
    nodejs  \
    npm \
    && rm -rf /var/lib/apt/lists/*

RUN install-php-extensions gd imap bcmath intl readline msgpack igbinary memcached mongodb ftp soap
#RUN install-php-extensions sqlite3
RUN docker-php-serversideup-set-id www-data $WWWUSER:$WWWGROUP
USER www-data
