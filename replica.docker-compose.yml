services:
  replica:
    image: 'mariadb:11'
    ports:
      - '${FORWARD_DB_PORT:-3306}:3306'
      - '${FORWARD_DB_REPLICA_PORT:-127.0.0.1:3307}:3306'
    environment:
      MYSQL_ROOT_PASSWORD: '${DB_PASSWORD}'
      MYSQL_ROOT_HOST: '%'
#      MYSQL_DATABASE: '${DB_DATABASE}'
      MYSQL_USER: '${DB_USERNAME}'
      MYSQL_PASSWORD: '${DB_PASSWORD}'
      MYSQL_ALLOW_EMPTY_PASSWORD: 'yes'
      MARIADB_MYSQL_LOCALHOST_USER: 1
#      MARIADB_REPLICATION_USER: '${MARIADB_REPLICATION_USER}'
#      MARIADB_REPLICATION_PASSWORD: '${MARIADB_REPLICATION_PASSWORD}'
#      MARIADB_MASTER_HOST: '${MARIADB_MASTER_HOST}'
    volumes:
      - './docker/mysql/data:/var/lib/mysql'
      - './docker/mysql/replica_conf:/etc/mysql/conf.d'
      - './docker/mysql/schema:/schema'
      - './docker/mysql/scripts:/scripts'
      - './docker/mysql/backup:/backup'
    network_mode: host
    container_name: replica
    restart: always
    healthcheck:
      test: ["CMD", "healthcheck.sh", "--su-mysql", "--connect", "--innodb_initialized"]
      retries: 3
      interval: 5s
      start_period: 5s
      timeout: 5s
