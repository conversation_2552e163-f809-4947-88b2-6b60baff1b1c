<?php

namespace App\Console\Commands;

use App\Enums\AccountingTypesPL;
use App\Enums\DocumentTypes;
use App\Enums\PartnerBusinessTypes;
use App\Enums\PartnerVATTypes;
use App\Enums\PaymentTypes;
use App\Enums\SystemModules;
use App\Enums\TaxResidencyCountries;
use App\Enums\TaxTypePL;
use App\Enums\TradeDocVatMethod;
use App\Helpers\Identifiers;
use App\Models\DocumentSeriesPattern;
use App\Models\DTOBankData;
use App\Models\DTOTradeDocMeta;
use App\Models\Installation;
use App\Models\Partner;
use App\Models\Products;
use App\Models\PurchaseDoc;
use App\Models\PurchaseDocItem;
use App\Models\Tenant;
use App\Models\TenantMeta;
use App\Models\TradeDoc;
use App\Models\TradeDocItem;
use App\Models\User;
use App\Repositories\CurrenciesRepository;
use App\Repositories\CurrencyRatesExchangeRepository;
use Illuminate\Console\Command;
use Illuminate\Database\ConnectionInterface;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Role;

class ImportOldData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-old-data {--partner= : Partner id to import}';

    protected Role $baseRole;

    protected $path = '/oldimport/';
    protected ConnectionInterface $con;

    protected array $createdUsers = [];
    protected array $createdPartners = [];
    protected array $createdDocSeries = [];

    protected array $vatMethods = [
        1 => TradeDocVatMethod::BASE_ON_GROSS,
        2 => TradeDocVatMethod::BASE_ON_NET,
        3 => TradeDocVatMethod::BASE_ON_GROSS,
    ];

    protected array $tradeDocTypes = [
        1 => DocumentTypes::FVS,
        2 => DocumentTypes::FAUP,
        4 => DocumentTypes::FVK,
    ];

    protected array $purchaseDocTypes = [
        1 => DocumentTypes::FZV,
        2 => DocumentTypes::FZUP
    ];

    protected array $paymentTypes = [
        1 => PaymentTypes::CASH,
        2 => PaymentTypes::BANK_TRANSFER,
        3 => PaymentTypes::PREPAID,
        4 => PaymentTypes::CASH_ON_DELIVERY,
    ];

    protected array $partnerUser = [
        25 => 92,
        26 => 93,
        43 => 94,
    ];

    protected array $tenantVatTypes = [
        25 => PartnerVATTypes::EU,
        26 => PartnerVATTypes::NOTVAT,
        43 => PartnerVATTypes::EU,
    ];

    protected array $tenantTaxTypes = [
        25 => TaxTypePL::LINEAR,
        26 => TaxTypePL::PROGRESSIVE,
        43 => TaxTypePL::PROGRESSIVE,
    ];

    /**
     * @var array $documentsThatHaveCorrection [$oldDocId => $newDocUUID]
     */
    protected array $documentsThatHaveCorrection = [];

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (env('OLD_IMPORT_ENABLED') !== 'enabled') {
            $this->info('Old import disabled');
            return;
        }


        $this->baseRole = Role::where('name', '=', 'Tenant Admin')->first();
        $this->info('Importing old data');
        $this->con = DB::connectUsing('oldbd', [
            'driver' => 'mysql',
            'host' => env('OLD_DB_HOST'),
            'port' => env('DB_PORT'),
            'database' => env('OLD_DB_DATABASE'),
            'username' => env('OLD_DB_USERNAME'),
            'password' => env('OLD_DB_PASSWORD'),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'strict' => false,
            'engine' => null,
        ]);

        if (null !== $partner_id = $this->option('partner')) {
            $tenant = $this->migrateTenant($partner_id);
            $this->updateExchangeRate();
            $this->updateDocumentSeries($tenant);
            $this->warn('Do not forget to disable import in env file');
            exit();
        }
        $this->migrateTenants();
        $this->updateExchangeRate();
        $this->updateDocumentSeries();
        $this->warn('Do not forget to disable import in env file');
    }

    public function migrateTenant(int $id): Tenant
    {
        $this->info('Migrating tenant ' . $id);
        $row = $this->con->selectOne('SELECT * FROM fx_partners WHERE id = ?', [$id]);
        if (empty($row)) {
            $this->info('Cannot find partner for id ' . $id);
            exit();
        }
        $tenant = $this->makeTenantModel($row);
        $this->makeTenantMetaModel($row, $tenant);
        $this->putLog(implode(',', [$row->id, $tenant->id]), 'tenants.csv', true);
        $user = $this->migrateTenantUsers($tenant, $this->partnerUser[$row->id]);
        $this->putLog(implode(',', [$this->partnerUser[$row->id], $user->id, $tenant->id]), 'users.csv', true);
        $this->migrateTenantProducts($tenant, $row->id);
        $this->migratePurchaseDocs($tenant, $user, $row);
        $this->migrateTradeDocs($tenant, $user, $row);
        return $tenant;
    }


    public function migrateTenants()
    {
        $this->info('Migrating tenants');
        $rows = $this->con->select('SELECT * FROM fx_partners');
        foreach ($rows as $row) {
            if (!array_key_exists($row->id, $this->partnerUser)) {
                continue;
            }
            $this->createdPartners = [];
            $tenant = $this->makeTenantModel($row);
            $this->makeTenantMetaModel($row, $tenant);
            $this->putLog(implode(',', [$row->id, $tenant->id]), 'tenants.csv', true);
            $user = $this->migrateTenantUsers($tenant, $this->partnerUser[$row->id]);
            $this->putLog(implode(',', [$this->partnerUser[$row->id], $user->id, $tenant->id]), 'users.csv', true);
            $this->migrateTenantProducts($tenant, $row->id);
            $this->migratePurchaseDocs($tenant, $user, $row);
            $this->migrateTradeDocs($tenant, $user, $row);
        }
    }

    protected function migratePurchaseDocs(Tenant $tenant, $user, $row)
    {
        $this->info('Migrating purchase docs: ' . $tenant->name);
        $docs = $this->con->select(
            'SELECT * FROM fx_purdoc WHERE installation = ' . $row->id .
            ' ORDER BY rocznik, data_wystawienia'
        );
        foreach ($docs as $doc) {
            if (!array_key_exists($doc->typ_dokumentu, $this->purchaseDocTypes)) {
                continue;
            }
            $purchasedoc = $this->makePurchaseDocModel($doc, $tenant, $user);
            $this->putLog(implode(',', [$doc->id, $purchasedoc->uuid, $tenant->id]), 'purchase.csv');
        }
    }


    protected function makePurchaseDocModel(\stdClass $record, Tenant $tenant, User $user): PurchaseDoc
    {

        $issuer = $this->createdPartners[$record->wystawca] ?? null;

        if (blank($issuer)) {
            $issuer = $this->migrateTenantPartners($tenant, $record->wystawca)->id;
        }

        if (blank($issuer)) {
            $this->info('Cannot find issuer for record: ' . $record->wystawca);
            exit();
        }

        $oldMeta = $this->con->selectOne('SELECT * FROM fx_purdoc_gz where fk_purdoc_id = ' . $record->id);
        $oldMeta = json_decode(gzinflate($oldMeta->gz), false);

        $purchaseDoc = new PurchaseDoc();
        $purchaseDoc->installation = $tenant->id;
        $purchaseDoc->seller_id = $issuer;
        $purchaseDoc->issuer_id = $issuer;
        $purchaseDoc->buyer_id = $tenant->id;
        $purchaseDoc->type = $this->purchaseDocTypes[$record->typ_dokumentu];
        $purchaseDoc->full_doc_number = $record->nr_dokumentu;
        $purchaseDoc->payment_type = $this->paymentTypes[$record->forma_platnosci_id];
        $purchaseDoc->payment_type_label = $this->paymentTypes[$record->forma_platnosci_id]->label();
        $purchaseDoc->payment_credit_days = $record->termin_platnosci_dni;
        $purchaseDoc->payment_due_date = $record->termin_platnosci_data;
        $purchaseDoc->payment_date = $record->data_oplacenia;
        $purchaseDoc->is_paid = (bool)$record->oplacona;

        $purchaseDoc->vat_method = $purchaseDoc->type === DocumentTypes::FZUP ?
            TradeDocVatMethod::BASE_ON_GROSS :
            $this->vatMethods[$record->metoda_vat];
        $purchaseDoc->net = $record->netto / 100;
        $purchaseDoc->vat_amount = $record->vat / 100;
        $purchaseDoc->gross = $record->brutto / 100;
        $purchaseDoc->issued_at = $record->data_wystawienia;
        $purchaseDoc->sells_date = $record->data_sprzedazy;
        $purchaseDoc->creator_id = $user->id;
        $purchaseDoc->save();
        $this->createdDocs[$record->id] = $purchaseDoc->uuid;

        $this->createPurchaseMetaOnModel($purchaseDoc, $oldMeta);
        $this->createPurchaseDocItems($purchaseDoc, $oldMeta, $record->id);
        return $purchaseDoc;
    }

    public function createPurchaseDocItems(PurchaseDoc $tradeDoc, \stdClass $meta, int $tradeDocId): void
    {
        $this->info('Creating items for purchase doc: ' . $tradeDoc->uuid);
        $items = $this->con->select('SELECT * FROM fx_purdoc_pozycje WHERE fk_purdoc_id = ' . $tradeDocId);

        foreach ($items as $item) {
            $tdItem = new PurchaseDocItem();
            $tdItem->installation = $tradeDoc->installation;
            $tdItem->label = $item->nazwa;
            $tdItem->amount = $item->ilosc / 100;
            $vat_rate = !is_numeric($item->stawka_vat) ? 0 : (int)$item->stawka_vat;
            if ($tradeDoc->vat_method === TradeDocVatMethod::BASE_ON_GROSS) {
                $tdItem->gross_unit_price = $item->cena_bazowa / 100;
                $tdItem->discounted_unit_price = $item->cena_bazowa_pr / 100;
                $tdItem->net_unit_price = $item->cena_jednostkowa_netto / 100;
                $tdItem->unit_type = $item->jm;
                $tdItem->net_value = $item->wartosc_netto / 100;
                $tdItem->vat_rate = $vat_rate;
                $tdItem->vat_label = $vat_rate;
                $tdItem->gross_value = $item->wartosc_brutto / 100;
                $tdItem->vat_value = $tdItem->gross_value - $tdItem->net_value;
            } else {
                $tdItem->net_unit_price = $item->cena_bazowa / 100;
                $tdItem->discounted_unit_price = $item->cena_bazowa_pr / 100;
                $tdItem->gross_unit_price = $item->cena_jednostkowa_netto / 100 * (100 + $vat_rate) / 100;
                $tdItem->unit_type = $item->jm;
                $tdItem->net_value = $item->wartosc_netto / 100;
                $tdItem->vat_rate = $vat_rate;
                $tdItem->vat_label = $vat_rate;
                $tdItem->gross_value = $item->wartosc_brutto / 100;
                $tdItem->vat_value = $tdItem->gross_value - $tdItem->net_value;
            }
            $tradeDoc->items()->save($tdItem);
        }
    }

    public function createPurchaseMetaOnModel(PurchaseDoc $record, \stdClass $meta, array $data = []): void
    {
        $this->info('Creating meta for purchase doc: ' . $record->uuid);
        $bank_data = [
            "account_name" => "",
            "bank_name" => "",
            "bank_account" => "",
            "bank_swift" => "",
            "bank_iban" => null,
            "bank_currency" => "",
        ];
        $vat_summary = [
            'vat_amount' => $meta->doc->summary?->sumVat ?? 0,
            'net_amount' => $meta->doc->summary?->sumNetto ?? 0,
            'gross_amount' => $meta->doc->summary?->sumBrutto ?? 0,
        ];
        $byVat = [];
        $arrayVat = json_decode(json_encode($meta->doc->summary?->byVat ?? []), true);

        foreach ($arrayVat as $stv => $vat) {
            $stv = !is_numeric($stv) ? 0 : (int)$stv;
            $byVat[$stv] = [
                'vat_rate' => $stv,
                'vat_amount' => $vat['vat'],
                'net_amount' => $vat['netto'],
                'gross_amount' => $vat['brutto'],
            ];
        }
        $byVat['summary'] = $vat_summary;

        $recordMeta = new DTOTradeDocMeta(
            issuer_address: Arr::except($record->issuer->getAttributes() ?? [], ['id', 'config']),
            seller_address: Arr::except($record->issuer->getAttributes() ?? [], ['id', 'config']),
            buyer_address: Arr::except($record->buyer->getAttributes() ?? [], ['id', 'installation']),
            bank_data: $bank_data,
            options: [
                'reverse_charge' => (bool)($meta->doc->reverse_charge ?? false),
                'different_seller' => false,
                'mpp' => false,
            ],
            note: $meta->doc->uwagi ?? null,
            vat: $byVat ?? [],
            duplicates: [],
            vat_source: [],
            vat_final: [],
            items: [],
            corrected_doc: [],
        );
        $record->meta()->create(['meta' => $recordMeta]);
    }


    protected function migrateTenantProducts(Tenant $tenant, $partnerId)
    {
        $this->info('Migrating products: ' . $partnerId);
        $rows = $this->con->select('SELECT * FROM fx_partners_products WHERE installation = ' . $partnerId);
        foreach ($rows as $row) {
            $product = new Products();
            $product->name = $row->nazwaproduktu;
            $product->hash = Identifiers::getRandomHash();
            $product->manufacturer_id = null;
            $product->price_per_unit = $row->cenabrutto / 100;
            $product->is_net = false;
            $product->vat_rate = $row->stvat;
            $product->vat_label = $row->stvat;
            $product->basic_unit = $row->jm;
            $product->volume_ml = null;
            $product->weight_gr = null;
            $product->limited_stock = true;
            $product->below_stock = false;
            $product->item_type = $row->typ_produktu;
            $product->description = $row->uwagi;
            $product->grace_period = null;
            $product->minimum_stock = 0;
            $product->minimum_exp_date = null;
            $product->gtin = $row->ean ?? null;
            $product->extra_code = $row->kod ?? null;
            $product->ext_link = '';
            $product->is_active = true;
            $product->installation = $tenant->id;
            $product->data_source = null;
            $product->data_source_identifier = null;
            $product->save();
            $this->putLog(implode(',', [$row->id, $product->id, $tenant->id]), 'products.csv', true);
        }
        $this->info('Products migrated: ' . $partnerId);
    }

    protected function migrateTradeDocs(Tenant $tenant, User $user, $row)
    {
        $this->info('Migrating trade docs: ' . $tenant->name);
        $docs = $this->con->select(
            'SELECT * FROM fx_dokumenty WHERE installation = ' . $row->id .
            ' ORDER BY rocznik, seria_dokumentu, nr_w_serii'
        );
        foreach ($docs as $doc) {
            if (!array_key_exists($doc->typ_dokumentu, $this->tradeDocTypes)) {
                continue;
            }
            if ($this->tradeDocTypes[$doc->typ_dokumentu] === DocumentTypes::FVK) {
                $this->info('Cannot migrate FVK');
                continue;
            }
            $tradedoc = $this->makeTradeDocModel($doc, $tenant, $user);
            $this->putLog(implode(',', [$doc->id, $tradedoc->uuid, $tenant->id]), 'trade.csv');
        }
    }

    protected function makeTenantModel(\stdClass $row): Tenant
    {
        $this->info('Creating tenant: ' . $row->name);
        $tenant = new Tenant();
        $tenant->name = $row->name;
        $tenant->hash = bin2hex(random_bytes(16));
        $tenant->vat_id = $row->nip;
        $tenant->phone = $row->phone;
        $tenant->email = $row->email;
        $tenant->postcode = $row->postalcode;
        $tenant->city = $row->city;
        $tenant->address = $row->address;
        $tenant->vat_type = $this->tenantVatTypes[$row->id];
        $tenant->tax_residency_country = TaxResidencyCountries::PL;
        $tenant->tax_type = $this->tenantTaxTypes[$row->id]->value;
        $tenant->accounting_type = AccountingTypesPL::IOR->value;
        $tenant->business_type = PartnerBusinessTypes::INDIVIDUAL->value;
        $tenant->is_active = 1;
        $tenant->config = [
            'modules' => [
                SystemModules::INVOICES->value,
                SystemModules::PURCHASE_INVOICES->value
            ],
        ];
        $tenant->save();
        $this->info('Tenant created: ' . $row->name);
        return $tenant;
    }

    protected function makeTenantMetaModel(\stdClass $row, Tenant $tenant): TenantMeta
    {
        $this->info('Creating tenant meta: ' . $row->name);
        $oldmeta = $this->con->select('SELECT * FROM fx_partners_meta WHERE current = 1 AND id_parent = ' . $row->id);
        $tmp_accounts = [];
        $accounts = [];
        foreach ($oldmeta as $old) {
            if ($old->typ_meta !== 'bankdata') {
                continue;
            }
            $accid = Str::afterLast($old->properties, '_');
            $label = Str::beforeLast($old->properties, '_');
            $tmp_accounts[(int)$accid][$label] = $old->value;
            if ($label === 'bank_name') {
                $tmp_accounts[(int)$accid]['account_name'] = $old->value;
            }
        }

        foreach ($tmp_accounts as $key => $account) {
            $accounts[] = DTOBankData::make($account);
        }

        $meta = new TenantMeta();
        $meta->meta = [
            'accounting' => [
                'regon' => $row->regon ?? null,
                'bdo' => null,
            ],
            "bank_accounts" => $accounts,
        ];
        $tenant->meta()->save($meta);
        $this->info('Tenant meta created: ' . $row->name);
        return $meta;
    }

    protected function migrateTenantUsers(Tenant $tenant, $userId): User
    {
        $this->info('Migrating user: ' . $userId);
        $row = $this->con->selectOne('SELECT * FROM fx_uzytkownik WHERE id = ' . $userId);
        $user = $this->makeUserModel($row);
        $inst = new Installation();
        $inst->tenant_id = $tenant->id;
        $inst->user_id = $user->id;
        $inst->save();
        $this->info('User migrated: ' . $userId);
        return $user;
    }

    protected function migrateTenantPartners(Tenant $tenant, $partnerId): Partner
    {
        $this->info('Migrating partner: ' . $partnerId);
        $row = $this->con->selectOne('SELECT * FROM fx_partners_clients WHERE id = ' . $partnerId);
        return $this->makePartnerModel($row, $tenant);
    }

    protected function makeUserModel(\stdClass $record): User
    {
        $user = new User();
        $user->name = $record->imie . ' ' . $record->nazwisko;
        $user->email = $record->email;
        $user->password = Hash::make('pass_' . $record->email);
        $user->save();
        $user->assignRole($this->baseRole);
        $this->createdUsers[$record->id] = $user->id;
        return $user;
    }

    protected function makePartnerModel(\stdClass $record, Tenant $tenant): Partner
    {
        $partner = new Partner();
        $partner->installation = $tenant->id;
        $partner->hash = Identifiers::getRandomHash();
        $partner->name = $record->name;
        $partner->short_name = $record->shortname ?? null;
        $partner->address = $record->address ?? null;
        $partner->postcode = $record->postalcode ?? null;
        $partner->city = $record->city ?? null;
        $partner->country_id = $record->country ?? TaxResidencyCountries::PL->name;
        $partner->phone = $record->phone ?? null;
        $partner->email = $record->email ?? null;
        $partner->contact_name = null;
        $partner->website = $record->website ?? null;
        $partner->bank_name = $record->bankname ?? null;
        $partner->bank_iban = null;
        $partner->bank_swift = null;
        $partner->bank_account = $record->bankaccount ?? null;
        $partner->vat_id = $record->nip ?? null;
        $partner->vat_type = PartnerVATTypes::LOCAL;
        $partner->business_type = PartnerBusinessTypes::INDIVIDUAL->value;
        $partner->tax_residency_country = TaxResidencyCountries::PL->name;
        $partner->save();
        $this->createdPartners[$record->id] = $partner->id;
        $this->putLog(implode(',', [$record->id, $partner->id, $tenant->id]), 'partners.csv');
        return $partner;
    }

    protected function makeTradeDocModel(\stdClass $record, Tenant $tenant, User $user): TradeDoc
    {

        $buyer = $this->createdPartners[$record->nabywca] ?? null;

        if (blank($buyer)) {
            $buyer = $this->migrateTenantPartners($tenant, $record->nabywca)->id;
        }

        if (blank($buyer)) {
            $this->info('Cannot find buyer for record: ' . $record->nabywca);
            exit();
        }

        $docSeries = $this->createdDocSeries[$record->seria_dokumentu] ?? null;
        if (blank($docSeries)) {
            $docSeries = $this->migrateDocSeries($record, $tenant);
        }

        $oldMeta = $this->con->selectOne('SELECT * FROM fx_dokumenty_gz where fk_dokumenty_id = ' . $record->id);
        $oldMeta = json_decode(gzinflate($oldMeta->gz), false);

        $tradeDoc = new TradeDoc();
        $tradeDoc->installation = $tenant->id;
        $tradeDoc->seller_id = $tenant->id;
        $tradeDoc->issuer_id = $tenant->id;
        $tradeDoc->buyer_id = $buyer;
        $tradeDoc->type = $this->tradeDocTypes[$record->typ_dokumentu];
        $tradeDoc->document_series_id = $docSeries->id;
        $tradeDoc->full_doc_number = $record->nr_dokumentu;
        $tradeDoc->doc_number = $record->nr_w_serii;
        $tradeDoc->transaction_id = Identifiers::createTransactionId($tradeDoc->type->name);
        $tradeDoc->payment_type = $this->paymentTypes[$record->forma_platnosci_id];
        $tradeDoc->payment_type_label = $this->paymentTypes[$record->forma_platnosci_id]->label();
        $tradeDoc->payment_credit_days = $record->termin_platnosci_dni;
        $tradeDoc->payment_due_date = $record->termin_platnosci_data;
        $tradeDoc->payment_date = $record->data_oplacenia;
        $tradeDoc->is_paid = (bool)$record->oplacona;

        $tradeDoc->currency = blank($oldMeta->doc->waluta ?? null) ? 'PLN' : $oldMeta->doc->waluta;
        $tradeDoc->exchange_rate = 1;

        $tradeDoc->vat_method = $tradeDoc->type === DocumentTypes::FAUP ?
            TradeDocVatMethod::BASE_ON_GROSS :
            $this->vatMethods[$record->metoda_vat];
        $tradeDoc->net = $record->netto / 100;
        $tradeDoc->vat_amount = $record->vat / 100;
        $tradeDoc->gross = $record->brutto / 100;
        $tradeDoc->issued_at = $record->data_wystawienia;
        $tradeDoc->sells_date = $record->data_sprzedazy;
        $tradeDoc->is_accepted = true;
        $tradeDoc->save();
        $this->createdDocs[$record->id] = $tradeDoc->uuid;

        $this->updateCounterSeriesNumber($tradeDoc, $docSeries);

        $this->createMetaOnModel($tradeDoc, $oldMeta);
        $this->createTradeDocItems($tradeDoc, $oldMeta, $record->id);
        return $tradeDoc;
    }

    public function createTradeDocItems(TradeDoc $tradeDoc, \stdClass $meta, int $tradeDocId): void
    {
        $this->info('Creating items for trade doc: ' . $tradeDoc->uuid);
        $items = $this->con->select('SELECT * FROM fx_dokumenty_pozycje WHERE fk_dokumenty_id = ' . $tradeDocId);

        foreach ($items as $item) {
            $tdItem = new TradeDocItem();
            $tdItem->installation = $tradeDoc->installation;
            $tdItem->label = $item->nazwa;
            $tdItem->amount = $item->ilosc / 100;
            $vat_rate = !is_numeric($item->stawka_vat) ? 0 : (int)$item->stawka_vat;
            if ($tradeDoc->vat_method === TradeDocVatMethod::BASE_ON_GROSS) {
                $tdItem->gross_unit_price = $item->cena_bazowa / 100;
                $tdItem->discounted_unit_price = $item->cena_bazowa_pr / 100;
                $tdItem->net_unit_price = $item->cena_jednostkowa_netto / 100;
                $tdItem->unit_type = $item->jm;
                $tdItem->net_value = $item->wartosc_netto / 100;
                $tdItem->vat_rate = $vat_rate;
                $tdItem->vat_label = $vat_rate;
                $tdItem->gross_value = $item->wartosc_brutto / 100;
                $tdItem->vat_value = $tdItem->gross_value - $tdItem->net_value;
            } else {
                $tdItem->net_unit_price = $item->cena_bazowa / 100;
                $tdItem->discounted_unit_price = $item->cena_bazowa_pr / 100;
                $tdItem->gross_unit_price = $item->cena_jednostkowa_netto / 100 * (100 + $vat_rate) / 100;
                $tdItem->unit_type = $item->jm;
                $tdItem->net_value = $item->wartosc_netto / 100;
                $tdItem->vat_rate = $vat_rate;
                $tdItem->vat_label = $vat_rate;
                $tdItem->gross_value = $item->wartosc_brutto / 100;
                $tdItem->vat_value = $tdItem->gross_value - $tdItem->net_value;
            }
            $tradeDoc->items()->save($tdItem);
        }
    }

    public function createMetaOnModel(TradeDoc $record, \stdClass $meta, array $data = []): void
    {
        $this->info('Creating meta for trade doc: ' . $record->uuid);
        $bank_data = [
            "account_name" => $meta->doc->bankdata[0]?->bank_name ?? null,
            "bank_name" => $meta->doc->bankdata[0]?->bank_name ?? null,
            "bank_account" => $meta->doc->bankdata[0]?->bank_account ?? null,
            "bank_swift" => $meta->doc->bankdata[0]?->bank_swift ?? null,
            "bank_iban" => null,
            "bank_currency" => $meta->doc->bankdata[0]?->bank_currency ?? null,
        ];
        $vat_summary = [
            'vat_amount' => $meta->doc->summary->sumVat,
            'net_amount' => $meta->doc->summary->sumNetto,
            'gross_amount' => $meta->doc->summary->sumBrutto,
        ];
        $byVat = [];
        $arrayVat = json_decode(json_encode($meta->doc->summary->byVat ?? []), true);

        foreach ($arrayVat as $stv => $vat) {
            $byVat[$stv] = [
                'vat_rate' => $stv,
                'vat_amount' => $vat['vat'],
                'net_amount' => $vat['netto'],
                'gross_amount' => $vat['brutto'],
            ];
        }
        $byVat['summary'] = $vat_summary;

        $recordMeta = new DTOTradeDocMeta(
            issuer_address: Arr::except($record->issuer->getAttributes() ?? [], ['id', 'config']),
            seller_address: Arr::except($record->issuer->getAttributes() ?? [], ['id', 'config']),
            buyer_address: Arr::except($record->buyer->getAttributes() ?? [], ['id', 'installation']),
            bank_data: $bank_data,
            options: [
                'reverse_charge' => (bool)($meta->doc->reverse_charge ?? false),
                'different_seller' => false,
                'mpp' => false,
            ],
            note: $meta->doc->uwagi ?? null,
            vat: $byVat ?? [],
            duplicates: [],
            vat_source: [],
            vat_final: [],
            items: [],
            corrected_doc: [],
        );
        $record->meta()->create(['meta' => $recordMeta]);
    }

    protected function migrateDocSeries(\stdClass $record, Tenant $tenant): DocumentSeriesPattern
    {
        $this->info('Creating tenant meta: ' . $record->seria_dokumentu);
        $old = $this->con->selectOne('SELECT * FROM fx_dokumenty_serie WHERE id = ' . $record->seria_dokumentu);
        return $this->makeDocSeries($old, $tenant, $record);
    }

    protected function makeDocSeries(\stdClass $record, Tenant $tenant, \stdClass $oldTradeDoc): DocumentSeriesPattern
    {
        $ds = new DocumentSeriesPattern();
        $ds->installation = $tenant->id;
        $ds->doc_type = $this->tradeDocTypes[$record->fk_dokumenty_typy_id];
        $ds->name = $record->nazwa;
        $ds->pattern = $record->seria;
        $ds->switch = $record->switch;
        $ds->save();
        $ds->createSeriesNumberRow(Carbon::createFromFormat('Y-m-d', $oldTradeDoc->data_wystawienia));
        if (DocumentSeriesPattern::query()
                ->active()
                ->default()
                ->docType($ds->doc_type)
                ->where('installation', $tenant->id)
                ->count() === 0
        ) {
            $ds->is_default = true;
            $ds->save();
        }

        $this->createdDocSeries[$oldTradeDoc->seria_dokumentu] = $ds;
        return $ds;
    }

    protected function updateCounterSeriesNumber(TradeDoc $record, DocumentSeriesPattern $dsp): void
    {
        $numbers = $dsp->lastSeriesCounterForDate($record->issued_at)->first();
        if (blank($numbers)) {
            $dsp->createSeriesNumberRow($record->issued_at);
            $numbers = $dsp->lastSeriesCounterForDate($record->issued_at)->first();
            if (blank($numbers)) {
                throw new \Exception('Cannot create series number');
            }
        }
        $numbers->counter = $record->doc_number;
        $numbers->save();
    }

    protected function updateExchangeRate()
    {
        $rows = TradeDoc::where('currency', '<>', 'PLN')->where('exchange_rate', '=', 1)->get();
        $this->info('Updating exchange rates for: ' . count($rows));
        foreach ($rows as $row) {
            /** @var TradeDoc $row */

            $startDate = $row->issued_at->copy()->subDays(1);
            [$rate, $effDate] = CurrencyRatesExchangeRepository::getNearestRate('PLN', $row->currency, $startDate);
            $this->info('GOT RATE1: ' . $rate . ' for date ' . $effDate);
            if (null === $rate) {
                $effDate = null;
                $counter = 10;
                do {
                    $startDate->subDay();
                    CurrencyRatesExchangeRepository::updateExchangeRates('PLN', $startDate);
                    $rate = CurrenciesRepository::getExchangeRate('PLN', $row->currency, $startDate);
                    $this->info('GOT RATE: ' . $rate . ' for date ' . $startDate->format('Y-m-d'));
                } while ($rate === null && $counter-- > 0);
            }
            $row->exchange_rate = $rate ?? 1;
            $row->currency_rate_date = is_null($rate) ? null : ($effDate ?? $startDate->format('Y-m-d'));
            $row->save();
            $this->info('Updating exchange rates for: ' . $row->full_doc_number);
            $this->info('Rate' . $rate . ' date: ' . $startDate->format('Y-m-d'));
        }
    }

    protected function updateDocumentSeries(Tenant|null $tenant = null): void
    {
        $rows = match ($tenant === null) {
            false => DocumentSeriesPattern::where('installation', $tenant->id)->get(),
            default => DocumentSeriesPattern::all(),
        };
        $this->info('Updating DOC series');
        $defaults = [];
        foreach ($rows as $row) {
            if (filled($defaults[$row->installation][$row->doc_type->value] ?? [])) {
                $row->is_default = false;
                $row->save();
                continue;
            }
            $defaults[$row->installation][$row->doc_type->value] = $row->pattern;
        }
    }

    protected function putLog(string $message, $filename, $append = false)
    {
        $path = $this->path . $filename;
        if ($append) {
            Storage::append($path, $message);
            return;
        }
        Storage::put($path, $message);
    }


    protected function getLog($filename)
    {
        $path = $this->path . $filename;
        return Storage::get($path);
    }
}
