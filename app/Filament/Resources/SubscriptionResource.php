<?php

namespace App\Filament\Resources;

use App\Enums\SubscriptionStatus;
use App\Filament\Resources\SubscriptionResource\Pages;
use App\Filament\Resources\SubscriptionResource\RelationManagers;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SubscriptionResource extends Resource
{
    protected static ?string $model = Subscription::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = 'Subskrypcje';
    protected static ?string $breadcrumb = 'Subskrypcje';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('tenant_id')
                    ->relationship('tenant', 'name')
                    ->label('Tenant')
                    ->preload()
                    ->searchable()
                    ->live()
                    ->afterStateUpdated(function (Forms\Set $set) {
                        $set('user_id', null);
                    })
                    ->required(),
                Forms\Components\Select::make('user_id')
                    ->options(function (Forms\Get $get) {
                        if (blank($get('tenant_id'))) {
                            return [];
                        }
                        return User::query()
                            ->whereHas('tenant', fn($q) => $q->where('tenant_id', $get('tenant_id')))
                            ->get()
                            ->pluck('email', 'id');
                    })
                    ->label('User')
                    ->searchable()
                    ->preload()
                    ->required(),
                Forms\Components\Select::make('plan_id')
                    ->relationship('plan', 'name')
                    ->label('Plan')
                    ->required(),
                Forms\Components\Select::make('status')
                    ->options(SubscriptionStatus::class)
                    ->required(),
                Forms\Components\DatePicker::make('starts_at')
                    ->live(true)
                    ->native(false)
                    ->closeOnDateSelection()
                    ->displayFormat('Y-m-d')
                    ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get, $state) {
                        if (blank($state) || blank($get('plan_id'))) {
                            return;
                        }
                        $plan = Plan::find($get('plan_id'));
                        $set('ends_at', (new \Carbon\Carbon($state))->addMonths($plan->period->value)->format('Y-m-d'));
                    })
                    ->required(),
                Forms\Components\DatePicker::make('ends_at')
                    ->native(false)
                    ->closeOnDateSelection()
                    ->displayFormat('Y-m-d')
                    ->required(),
                Forms\Components\DatePicker::make('trial_ends_at')
                    ->native(false)
                    ->closeOnDateSelection()
                    ->displayFormat('Y-m-d')
                ,
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('tenant.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.email')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('plan.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->searchable(),
                Tables\Columns\TextColumn::make('trial_ends_at')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('starts_at')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('ends_at')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->date()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                ->hiddenLabel(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSubscriptions::route('/'),
            'create' => Pages\CreateSubscription::route('/create'),
            'edit' => Pages\EditSubscription::route('/{record}/edit'),
        ];
    }
}
