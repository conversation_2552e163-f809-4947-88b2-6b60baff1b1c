<?php

namespace App\Filament\App\Pages;

use App\Models\Payment;
use App\Enums\PaymentStatus;
use Filament\Pages\Page;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Infolist;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Contracts\HasInfolists;
use Filament\Support\Enums\FontWeight;
use Illuminate\Support\HtmlString;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class PaymentThankYou extends Page implements HasInfolists
{
    use InteractsWithInfolists;

    protected static string $view = 'filament.app.pages.payment-thank-you';

    protected static bool $shouldRegisterNavigation = false;
    
    public ?Payment $payment = null;
    public string $paymentId;
    public bool $paymentFound = false;
    public string $errorMessage = '';

    public function mount(string $paymentId): void
    {
        $this->paymentId = $paymentId;
        $this->loadPayment();
    }

    protected function loadPayment(): void
    {
        try {
            // Try to find payment by ID first, then by provider_payment_id
            $this->payment = Payment::with(['user', 'tenant', 'subscription.plan'])
                ->where(function ($query) {
                    $query->where('id', $this->paymentId)
                          ->orWhere('provider_payment_id', $this->paymentId);
                })
                ->firstOrFail();
            
            $this->paymentFound = true;
        } catch (ModelNotFoundException $e) {
            $this->paymentFound = false;
            $this->errorMessage = 'Nie znaleziono płatności o podanym identyfikatorze.';
        }
    }

    public function getHeading(): string
    {
        if (!$this->paymentFound) {
            return 'Błąd płatności';
        }

        return match ($this->payment->status) {
            'completed' => 'Dziękujemy za płatność!',
            'pending' => 'Płatność w trakcie realizacji',
            'failed' => 'Płatność nieudana',
            'error' => 'Wystąpił błąd podczas płatności',
            default => 'Status płatności'
        };
    }

    public function getSubheading(): ?string
    {
        if (!$this->paymentFound) {
            return 'Sprawdź poprawność linku lub skontaktuj się z obsługą klienta.';
        }

        return match ($this->payment->status) {
            'completed' => 'Twoja płatność została pomyślnie zrealizowana.',
            'pending' => 'Twoja płatność jest obecnie przetwarzana. Otrzymasz powiadomienie po zakończeniu.',
            'failed' => 'Niestety, płatność nie została zrealizowana. Spróbuj ponownie lub skontaktuj się z obsługą.',
            'error' => 'Wystąpił błąd techniczny podczas przetwarzania płatności.',
            default => 'Sprawdź szczegóły płatności poniżej.'
        };
    }

    public function getHeaderActions(): array
    {
        if (!$this->paymentFound || $this->payment->status !== 'completed') {
            return [];
        }

        return [
            \Filament\Actions\Action::make('continue')
                ->label('Przejdź do aplikacji')
                ->icon('heroicon-o-arrow-right')
                ->color('success')
                ->url('/app')
        ];
    }

    public function paymentInfolist(): array
    {
        if (!$this->paymentFound) {
            return [];
        }

        return [
            Section::make('Szczegóły płatności')
                ->schema([
                    Grid::make(2)
                        ->schema([
                            TextEntry::make('amount')
                                ->label('Kwota')
                                ->formatStateUsing(fn($state) => number_format($state / 100, 2, ',', ' ') . ' ' . $this->payment->currency)
                                ->weight(FontWeight::Bold),
                            TextEntry::make('status')
                                ->label('Status')
                                ->formatStateUsing(fn($state) => $this->getStatusLabel($state))
                                ->badge()
                                ->color(fn($state) => $this->getStatusColor($state)),
                            TextEntry::make('provider_payment_id')
                                ->label('Numer transakcji')
                                ->copyable()
                                ->hidden(fn($state) => empty($state)),
                            TextEntry::make('paid_at')
                                ->label('Data płatności')
                                ->dateTime('d.m.Y H:i')
                                ->hidden(fn($state) => empty($state)),
                        ]),
                ]),
            
            Section::make('Szczegóły subskrypcji')
                ->schema([
                    Grid::make(2)
                        ->schema([
                            TextEntry::make('subscription.plan.name')
                                ->label('Plan')
                                ->weight(FontWeight::Bold)
                                ->hidden(fn() => !$this->payment->subscription),
                            TextEntry::make('subscription.starts_at')
                                ->label('Data rozpoczęcia')
                                ->date('d.m.Y')
                                ->hidden(fn() => !$this->payment->subscription),
                            TextEntry::make('subscription.ends_at')
                                ->label('Data zakończenia')
                                ->date('d.m.Y')
                                ->hidden(fn() => !$this->payment->subscription),
                            TextEntry::make('subscription.plan.description')
                                ->label('Opis planu')
                                ->columnSpanFull()
                                ->hidden(fn() => !$this->payment->subscription || empty($this->payment->subscription->plan->description)),
                        ]),
                ])
                ->hidden(fn() => !$this->payment->subscription),
        ];
    }

    protected function getStatusLabel(string $status): string
    {
        return match ($status) {
            'completed' => 'Zakończona',
            'pending' => 'W trakcie',
            'failed' => 'Nieudana',
            'error' => 'Błąd',
            default => 'Nieznany'
        };
    }

    protected function getStatusColor(string $status): string
    {
        return match ($status) {
            'completed' => 'success',
            'pending' => 'warning',
            'failed' => 'danger',
            'error' => 'danger',
            default => 'gray'
        };
    }

    public function getIcon(): ?string
    {
        if (!$this->paymentFound) {
            return 'heroicon-o-exclamation-triangle';
        }

        return match ($this->payment->status) {
            'completed' => 'heroicon-o-check-circle',
            'pending' => 'heroicon-o-clock',
            'failed' => 'heroicon-o-x-circle',
            'error' => 'heroicon-o-exclamation-triangle',
            default => 'heroicon-o-information-circle'
        };
    }

    public function getIconColor(): string
    {
        if (!$this->paymentFound) {
            return 'danger';
        }

        return match ($this->payment->status) {
            'completed' => 'success',
            'pending' => 'warning',
            'failed' => 'danger',
            'error' => 'danger',
            default => 'gray'
        };
    }
}
