<?php

namespace App\Services\Payments;

use App\Contracts\PaymentProviderContract;
use App\Enums\PaymentStatus;
use App\Enums\SubscriptionStatus;
use App\Models\Payment;
use App\Models\Plan;
use App\Models\User;
use App\Models\Subscription;
use Illuminate\Filesystem\Filesystem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class PayUPaymentProvider implements PaymentProviderContract
{

    public null|Subscription $subscription = null;
    public null|Plan $plan = null;
    public null|Payment $payment = null;

    public function __construct()
    {
        $this->setupPayU();
    }

    public function createSubscription(User $user, Plan $plan, array $options = []): mixed
    {

        /**
         * @var Subscription $sub
         */
        $sub = Subscription::create([
            'user_id' => $user->id,
            'tenant_id' => $user->installation(),
            'plan_id' => $plan->id,
            'price' => $plan->price,
            'status' => SubscriptionStatus::NEW,
            'starts_at' => now(),
            'ends_at' => now()->addMonths($plan->period->value),
        ]);
        $sub->setRelation('plan', $plan);
        $this->subscription = $sub;
        $this->plan = $plan;
        $this->charge($user, $sub, ['subscription_id' => $sub->id]);
        return $sub;
    }

    public function cancelSubscription(Subscription $subscription): bool
    {
        // PayU nie wspiera subskrypcji natywnie - możesz anulować z poziomu aplikacji
        return true;
    }

    public function getSubscriptionStatus(Subscription $subscription): string
    {
        // Zwraca lokalny status lub wykonuje zapytanie do API (jeśli obsługujesz recurring przez PayU)
        return $subscription->status;
    }

    public function charge(User $user, Subscription $subscription, array $options = []): mixed
    {
        $order['continueUrl'] = config('app.url').'/app/thank-you';
        $order['notifyUrl'] = route('payments-webhook', ['provider' => 'payu']);
        $order['customerIp'] = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
        $order['merchantPosId'] = \OpenPayU_Configuration::getMerchantPosId();
        $order['description'] = 'Subskrypcja ' . $subscription->getOrderId();
        $order['currencyCode'] = $options['currency'] ?? 'PLN';
        $order['totalAmount'] = (int) ($subscription->plan->price * 100);
        $order['extOrderId'] = $subscription->getOrderId(); //must be unique!

        $order['products'][0]['name'] = $subscription->plan->name;
        $order['products'][0]['unitPrice'] = (int) ($subscription->plan->price * 100);
        $order['products'][0]['quantity'] = 1;

        $order['buyer']['email'] = $user->email;
        $order['buyer']['firstName'] = $user->profile->name ?? '';
        $order['buyer']['lastName'] = $user->profile->surname ?? '';

//        dd($order);


        try {
            $response = \OpenPayU_Order::create($order);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return null;
        }

        if (null === $response) {
            Log::error('Payment response is empty');
            return null;
        }


        $this->payment = Payment::create([
            'user_id' => $user->id,
            'tenant_id' => $user->tenant_id,
            'subscription_id' => $subscription->id,
            'provider' => static::class,
            'provider_payment_id' => $response->getResponse()->orderId,
            'amount' => $subscription->plan->attributes['price'],
            'currency' => $options['currency'] ?? 'PLN',
            'status' => $response->getResponse()->status->statusCode === 'SUCCESS' ?
                PaymentStatus::PENDING :
                PaymentStatus::ERROR,
            'meta' => $response->getResponse(),
        ]);

        return $response->getResponse()->redirectUri;
    }

    public function handleWebhook(Request $request): void
    {
        Storage::disk('local')->put('payu.json', $request->json());
        // Obsługa webhooka od PayU
    }

    public function getName(): string
    {
        return 'payu';
    }

    protected function getPayUConfig(): array
    {
        return [
            'pos_id' => config('services.payu.pos_id'),
            'second_key' => config('services.payu.second_key'),
            'client_id' => config('services.payu.client_id'),
            'client_secret' => config('services.payu.client_secret'),
            'environment' => config('services.payu.environment'),
            'webhook_url' => config('services.payu.webhook_code'),
        ];
    }

    protected function setupPayU(): void
    {
        $config = $this->getPayUConfig();

        \OpenPayU_Configuration::setEnvironment($config['environment']);
        \OpenPayU_Configuration::setMerchantPosId($config['pos_id']);
        \OpenPayU_Configuration::setSignatureKey($config['second_key']);
        \OpenPayU_Configuration::setOauthClientId($config['client_id']);
        \OpenPayU_Configuration::setOauthClientSecret($config['client_secret']);
        (new Filesystem())->makeDirectory(
            path: storage_path('framework/cache/payu'),
            mode: 0775,
            recursive: true,
            force: true
        );
        \OpenPayU_Configuration::setOauthTokenCache(new \OauthCacheFile(storage_path('framework/cache/payu')));
    }
}
