<?php

namespace App\Services\Payments;

use App\Contracts\PaymentProviderContract;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Http\Request;

class InternalPaymentProvider implements PaymentProviderContract
{

    /**
     * @inheritDoc
     */
    public function createSubscription(User $user, string $planId, array $options = []): mixed
    {
        // TODO: Implement createSubscription() method.
    }

    /**
     * @inheritDoc
     */
    public function cancelSubscription(Subscription $subscription): bool
    {
        // TODO: Implement cancelSubscription() method.
    }

    /**
     * @inheritDoc
     */
    public function getSubscriptionStatus(Subscription $subscription): string
    {
        // TODO: Implement getSubscriptionStatus() method.
    }

    /**
     * @inheritDoc
     */
    public function charge(User $user, float $amount, string $currency = 'PLN', array $options = []): mixed
    {
        // TODO: Implement charge() method.
    }

    /**
     * @inheritDoc
     */
    public function handleWebhook(Request $request): void
    {
        // TODO: Implement handleWebhook() method.
    }

    /**
     * @inheritDoc
     */
    public function getName(): string
    {
        // TODO: Implement getName() method.
    }
}
