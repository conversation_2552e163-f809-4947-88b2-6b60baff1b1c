<?php
namespace App\Services\Payments;

use App\Contracts\PaymentProviderContract;
use App\Models\User;
use App\Models\Subscription;
use Illuminate\Http\Request;

class Przelewy24PaymentProvider implements PaymentProviderContract
{
    public function createSubscription(User $user, string $planId, array $options = []): mixed
    {
        // Przelewy24 obsługuje tylko płatności jednorazowe – musisz samodzielnie je cyklicznie ponawiać
    }

    public function cancelSubscription(Subscription $subscription): bool
    {
        return true;
    }

    public function getSubscriptionStatus(Subscription $subscription): string
    {
        return $subscription->status;
    }

    public function charge(User $user, float $amount, string $currency = 'PLN', array $options = []): mixed
    {
        // Wysyłka do API Przelewy24 – płatność jednorazowa
    }

    public function handleWebhook(Request $request): void
    {
        // Obsługa powiadomień od P24 (np. transakcja zakończona sukcesem)
    }

    public function getName(): string
    {
        return 'przelewy24';
    }
}
