<?php

namespace App\Models;

use App\Enums\MoneyVOCast;
use App\Enums\SubscriptionStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Subscription extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'tenant_id',
        'plan_id',
        'status',
        'price',
        'starts_at',
        'trial_ends_at',
        'ends_at',
    ];

    protected $casts = [
        'trial_ends_at' => 'date',
        'starts_at' => 'date',
        'ends_at' => 'date',
        'price' => MoneyVOCast::class,
        'status' => SubscriptionStatus::class,
    ];


    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public static function __callStatic($method, $parameters)
    {
        return parent::__callStatic($method, $parameters); // TODO: Change the autogenerated stub
    }

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    public function getOrderId(): string
    {
        return 'S' . $this->id . 'T' . $this->tenant_id . $this->plan->getPlanCode();
    }
}
