<?php

namespace App\Models;

use App\Enums\MoneyVOCast;
use App\Enums\PlanPeriod;
use App\Enums\PlanType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Plan extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'price',
        'period',
        'features',
        'type',
        'is_active',
    ];

    protected $casts = [
        'features' => 'array',
        'is_active' => 'boolean',
        'period' => PlanPeriod::class,
        'type' => PlanType::class,
        'price' => MoneyVOCast::class,
    ];

    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    public function getPlanCode(): string
    {
        return 'P' . $this->id . ($this->type === PlanType::TRIAL ? 'TR' : 'PA') . $this->period->value . 'M';
    }
}
