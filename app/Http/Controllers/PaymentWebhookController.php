<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\Payments\StripePaymentProvider;
use App\Services\Payments\PayUPaymentProvider;
use App\Services\Payments\Przelewy24PaymentProvider;

class PaymentWebhookController extends Controller
{

    public function handlePayment(Request $request, string $provider)
    {
        return match ($provider) {
            'stripe' => $this->handleStripe($request),
            'payu' => $this->handlePayU($request),
            'przelewy24' => $this->handlePrzelewy24($request),
            default => response('Not found', 404)
        };
    }


    public function handleStripe(Request $request)
    {
        (new StripePaymentProvider())->handleWebhook($request);
        return response('OK', 200);
    }

    public function handlePayU(Request $request)
    {
        (new PayUPaymentProvider())->handleWebhook($request);
        return response('OK', 200);
    }

    public function handlePrzelewy24(Request $request)
    {
        (new Przelewy24PaymentProvider())->handleWebhook($request);
        return response('OK', 200);
    }
}
