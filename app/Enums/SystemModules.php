<?php

namespace App\Enums;

enum SystemModules: int
{
    use EnumHelper;

    case WAREHOUSE = 1;
    case INVOICES = 2;
    case SIMPLE_PRODUCTS = 3;
    case PURCHASE_INVOICES = 4;
    case CALENDAR = 5;
    case CLIENT_ORDERS = 6;
    case JOB_TASKS = 7;
    case SIMPLE_CHARTS = 8;
    case EMPLOYEES = 9;
    case LOGO = 10;
    case INVOICE_TEMPLATES = 11;

    public static function get_label($key): string
    {
        $labels = [
            1 => __('app.enums.system_modules.WAREHOUSE'),
            2 => __('app.enums.system_modules.INVOICES'),
            3 => __('app.enums.system_modules.SIMPLE_PRODUCTS'),
            4 => __('app.enums.system_modules.PURCHASE_INVOICES'),
            5 => __('app.enums.system_modules.CALENDAR'),
            6 => __('app.enums.system_modules.CLIENT_ORDERS'),
            7 => __('app.enums.system_modules.JOB_TASKS'),
            8 => __('app.enums.system_modules.SIMPLE_CHARTS'),
            9 => __('app.enums.system_modules.EMPLOYEES'),
            10 => __('app.enums.system_modules.LOGO'),
            11 => __('app.enums.system_modules.INVOICE_TEMPLATES'),
        ];
        return $labels[$key] ?? 'none';
    }

    public static function enabled()
    {
        return [
            self::WAREHOUSE,
            self::INVOICES,
            self::SIMPLE_PRODUCTS,
            self::PURCHASE_INVOICES,
            self::JOB_TASKS,
            self::SIMPLE_CHARTS,
            self::EMPLOYEES,
            self::LOGO,
            self::INVOICE_TEMPLATES,
        ];
    }

    public static function enabledToArrayWithLabels(): array
    {
//        $source = self::toArray();
        $source = [];
        foreach (self::enabled() as $value) {
            $source[$value->value] = $value->label();
        }
        return $source;
    }

    public function label()
    {
        return __('app.enums.system_modules.' . $this->name);
    }
}
