services:
#On stage only php-fpm is running. <PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON> are running on the server. UID and GID are the same as on the server (33:33)
  app:
    build:
      context: ./docker/app
      dockerfile: fpm.dockerfile
      args:
        WWWGROUP: '${WWWGROUP:-1000}'
        WWWUSER: '${WWWUSER:-1000}'
    image: twojefaktury/app
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    environment:
      WWWUSER: '${WWWUSER:-1000}'
      PHP_MEMORY_LIMIT: '${PHP_MEMORY_LIMIT:-512M}'
      PHP_MAX_EXECUTION_TIME: '${PHP_MAX_EXECUTION_TIME:-600}'
      PHP_MAX_INPUT_TIME: '${PHP_MAX_INPUT_TIME:-600}'
      PHP_UPLOAD_MAX_FILESIZE: '${PHP_UPLOAD_MAX_FILESIZE:-100M}'
      PHP_POST_MAX_SIZE: '${PHP_POST_MAX_SIZE:-100M}'
      PHP_DATE_TIMEZONE: '${PHP_DATE_TIMEZONE:-UTC}'
      PHP_OPCACHE_ENABLE: '${PHP_OPCACHE_ENABLE:-1}'
      PHP_ERROR_LOG: '/var/log/php-fpm/error.log'
      PHP_FPM_PM_CONTROL: 'dynamic'
      PHP_FPM_PM_START_SERVERS: '32'
      PHP_FPM_PM_MAX_CHILDREN: '120'
      PHP_FPM_PM_MAX_SPARE_SERVERS: '48'
      PHP_FPM_PM_MIN_SPARE_SERVERS: '16'
    volumes:
      - '.:/var/www/html/nativet'
      - './docker/app/logs:/var/log/php-fpm'
    working_dir: '/var/www/html/nativet'
    network_mode: host
    container_name: app
    restart: unless-stopped
    ports:
      - '127.0.0.1:9001:9000'

#  nginx:
#    extends:
#      file: server.docker-compose.yml
#      service: nginx
#    volumes:
#      - ./docker/nginx/${NGINX_CONF:-stage_ssl.conf}:/etc/nginx/conf.d/default.conf
#      - .:/var/www/html:delegated
#      - ./docker/nginx/logs:/var/log/nginx
#      - /etc/letsencrypt:/etc/letsencrypt:ro

  queue:
    image: twojefaktury/app
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    environment:
      WWWUSER: '${WWWUSER:-1000}'
      PHP_FPM_POOL_NAME: 'queue'
      PHP_ERROR_LOG: '/var/log/php/error.log'
      PHP_OPCACHE_ENABLE: '${PHP_OPCACHE_ENABLE:-1}'
      PHP_MEMORY_LIMIT: '${PHP_MEMORY_LIMIT:-512M}'
    volumes:
      - '.:/var/www/html/nativet'
      - './docker/queue/logs:/var/log/php'
    network_mode: host
    container_name: queue
    command: [ "php", "/var/www/html/nativet/artisan", "queue:work", "--sleep=3", "--tries=3", "--timeout=0", "--memory=512" ]
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "php", "artisan", "app:health" ]
      retries: 3
      timeout: 5s

  cron:
    container_name: cron
    image: twojefaktury/app
    stop_signal: SIGTERM
    volumes:
      - '.:/var/www/html/nativet'
      - './docker/cron/logs:/var/log/php'
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    environment:
      WWWUSER: '${WWWUSER}'
      PHP_FPM_POOL_NAME: 'cron'
      PHP_ERROR_LOG: '/var/log/php/error.log'
      PHP_OPCACHE_ENABLE: '${PHP_OPCACHE_ENABLE:-1}'
      PHP_MEMORY_LIMIT: '${PHP_MEMORY_LIMIT:-1024M}'
    network_mode: host
    healthcheck:
      test: [ "CMD", "php", "artisan", "app:health" ]
      retries: 3
      timeout: 5s
    command: [ "php", "/var/www/html/nativet/artisan", "schedule:work" ]

#networks:
#  twojefaktury:
#    driver: bridge
