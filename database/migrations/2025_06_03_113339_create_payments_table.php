<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('tenant_id')->constrained()->onDelete('cascade');
            $table->foreignId('subscription_id')->nullable()->constrained()->onDelete('set null');
            $table->string('provider')->nullable(); // np. 'stripe', 'paypal'
            $table->string('provider_payment_id')->nullable(); // ID płatności zewnętrznej
            $table->integer('amount', false, true);
            $table->string('currency', 3)->default('PLN');
            $table->string('status')->default('completed'); // completed, failed, refunded, pending
            $table->timestamp('paid_at')->nullable();
            $table->json('meta')->nullable(); // dowolne dodatkowe dane (webhook payload etc.)
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
