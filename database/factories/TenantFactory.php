<?php

namespace Database\Factories;

use App\Enums\AccountingTypesPL;
use App\Enums\PartnerBusinessTypes;
use App\Enums\PartnerVATTypes;
use App\Enums\TaxResidencyCountries;
use App\Enums\TaxTypePL;
use App\Helpers\Identifiers;
use App\Models\DTOAccountingData;
use App\Models\DTOBankData;
use App\Models\DTOTenantMetadata;
use App\Models\Tenant;
use App\Models\TenantMeta;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Tenant>
 */
class TenantFactory extends Factory
{
    protected $model = Tenant::class;

    /**
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $companyName = fake()->company();

        return [
            'hash' => Identifiers::getRandomHash(32),
            'name' => $companyName,
            'address' => fake()->streetAddress(),
            'postcode' => fake()->postcode(),
            'city' => fake()->city(),
            'phone' => fake()->phoneNumber(),
            'email' => fake()->companyEmail(),
            'contact_name' => fake()->name(),
            'website' => fake()->url(),
            'system_domain' => null, // Usually set separately
            'vat_id' => $this->generatePolishVatId(),
            'vat_type' => fake()->randomElement(PartnerVATTypes::cases()),
            'tax_residency_country' => TaxResidencyCountries::PL,
            'tax_type' => fake()->randomElement(TaxTypePL::cases()),
            'accounting_type' => fake()->randomElement(AccountingTypesPL::cases()),
            'business_type' => fake()->randomElement(PartnerBusinessTypes::cases()),
            'is_active' => true,
            'config' => null, // Can be set via state methods if needed
        ];
    }

    /**
     * Configure the factory to create tenant with metadata.
     */
    public function withMetadata(): static
    {
        return $this->afterCreating(function (Tenant $tenant) {
            $this->createTenantMetadata($tenant);
        });
    }

    /**
     * Configure the factory to create inactive tenant.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Configure the factory for individual business type.
     */
    public function individual(): static
    {
        return $this->state(fn (array $attributes) => [
            'business_type' => PartnerBusinessTypes::INDIVIDUAL,
            'vat_type' => PartnerVATTypes::NOTVAT, // Individuals often not VAT payers
        ]);
    }

    /**
     * Configure the factory for business entity.
     */
    public function business(): static
    {
        return $this->state(fn (array $attributes) => [
            'business_type' => PartnerBusinessTypes::BUSINESS,
            'vat_type' => PartnerVATTypes::LOCAL,
        ]);
    }

    /**
     * Generate a realistic Polish VAT ID.
     */
    private function generatePolishVatId(): string
    {
        // Generate 10-digit Polish VAT ID (NIP)
        $digits = '';
        for ($i = 0; $i < 10; $i++) {
            $digits .= fake()->numberBetween(0, 9);
        }

        // Format as XXX-XXX-XX-XX
        return substr($digits, 0, 3) . '-' . substr($digits, 3, 3) . '-' .
               substr($digits, 6, 2) . '-' . substr($digits, 8, 2);
    }

    /**
     * Create tenant metadata with realistic data.
     */
    private function createTenantMetadata(Tenant $tenant): void
    {
        // Create accounting data
        $accountingData = DTOAccountingData::make([
            'regon' => $this->generateRegonNumber(),
            'bdo' => strtoupper(fake()->bothify('??####??')),
        ]);

        // Create bank accounts
        $bankAccounts = collect([
            DTOBankData::make([
                'account_name' => 'Konto główne PLN',
                'bank_name' => fake()->randomElement([
                    'PKO Bank Polski', 'Bank Pekao', 'mBank', 'ING Bank Śląski',
                    'Santander Bank Polska', 'Bank Millennium', 'Alior Bank'
                ]),
                'bank_account' => $this->generatePolishBankAccount(),
                'bank_swift' => strtoupper(fake()->bothify('????PL??')),
                'bank_iban' => null,
                'bank_currency' => 'PLN',
            ]),
        ]);

        // Optionally add EUR account for some tenants
        if (fake()->boolean(30)) { // 30% chance
            $bankAccounts->push(DTOBankData::make([
                'account_name' => 'Konto EUR',
                'bank_name' => fake()->randomElement([
                    'PKO Bank Polski', 'Bank Pekao', 'mBank'
                ]),
                'bank_account' => $this->generatePolishBankAccount(),
                'bank_swift' => strtoupper(fake()->bothify('????PL??')),
                'bank_iban' => 'PL' . fake()->numerify('####################'),
                'bank_currency' => 'EUR',
            ]));
        }

        // Create empty images collection
        $images = collect();

        // Create invoice configuration
        $invoiceConfiguration = [
            'selected_template' => config('app.default_invoice_template', 'default'),
        ];

        // Create DTOTenantMetadata
        $metadata = new DTOTenantMetadata(
            accounting: $accountingData,
            bank_accounts: $bankAccounts,
            images: $images,
            invoice_configuration: $invoiceConfiguration
        );

        // Save to database
        TenantMeta::create([
            'tenant_id' => $tenant->id,
            'meta' => $metadata->toArray(),
        ]);
    }

    /**
     * Generate a realistic REGON number (9 digits).
     */
    private function generateRegonNumber(): string
    {
        return fake()->numerify('#########');
    }

    /**
     * Generate a realistic Polish bank account number.
     */
    private function generatePolishBankAccount(): string
    {
        // Generate 26-digit account number in Polish format
        $digits = fake()->numerify('####################');

        // Format as XX XXXX XXXX XXXX XXXX XXXX XXXX
        return substr($digits, 0, 2) . ' ' .
               substr($digits, 2, 4) . ' ' .
               substr($digits, 6, 4) . ' ' .
               substr($digits, 10, 4) . ' ' .
               substr($digits, 14, 4) . ' ' .
               substr($digits, 18, 4) . ' ' .
               substr($digits, 22, 4);
    }
}
