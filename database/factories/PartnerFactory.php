<?php

namespace Database\Factories;

use App\Enums\PartnerBusinessTypes;
use App\Enums\PartnerVATTypes;
use App\Enums\TaxResidencyCountries;
use App\Models\Partner;
use App\Models\Tenant;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Partner>
 */
class PartnerFactory extends Factory
{
    protected $model = Partner::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $companyName = fake()->company();
        $shortName = $this->generateShortName($companyName);

        return [
            'installation' => null, // Will be set via forTenant() method or state
            'hash' => null, // Will be auto-generated by model's save method
            'name' => $companyName,
            'short_name' => $shortName,
            'address' => fake()->streetAddress(),
            'postcode' => fake()->postcode(),
            'city' => fake()->city(),
            'country_id' => 'PL',
            'phone' => fake()->phoneNumber(),
            'email' => fake()->companyEmail(),
            'contact_name' => fake()->name(),
            'website' => fake()->boolean(70) ? fake()->url() : null,
            'bank_name' => fake()->randomElement([
                'PKO Bank Polski', 'Bank Pekao', 'mBank', 'ING Bank Śląski',
                'Santander Bank Polska', 'Bank Millennium', 'Alior Bank', 'Credit Agricole'
            ]),
            'bank_iban' => fake()->boolean(60) ? 'PL' . fake()->numerify('####################') : null,
            'bank_swift' => strtoupper(fake()->bothify('????PL??')),
            'bank_account' => $this->generatePolishBankAccount(),
            'vat_id' => $this->generatePolishVatId(),
            'vat_type' => fake()->randomElement(PartnerVATTypes::cases()),
            'business_type' => fake()->randomElement(PartnerBusinessTypes::cases()),
            'tax_residency_country' => TaxResidencyCountries::PL,
            'is_active' => true,
        ];
    }

    /**
     * Configure the factory for a specific tenant.
     */
    public function forTenant(Tenant $tenant): static
    {
        return $this->state(fn (array $attributes) => [
            'installation' => $tenant->id,
        ]);
    }

    /**
     * Configure the factory for individual business type.
     */
    public function individual(): static
    {
        return $this->state(function (array $attributes) {
            $firstName = fake()->firstName();
            $lastName = fake()->lastName();
            $fullName = $firstName . ' ' . $lastName;

            return [
                'name' => $fullName,
                'short_name' => $firstName . ' ' . $lastName[0] . '.',
                'business_type' => PartnerBusinessTypes::INDIVIDUAL,
                'vat_type' => fake()->boolean(30) ? PartnerVATTypes::LOCAL : PartnerVATTypes::NOTVAT,
                'website' => null, // Individuals less likely to have websites
            ];
        });
    }

    /**
     * Configure the factory for business entity.
     */
    public function business(): static
    {
        return $this->state(fn (array $attributes) => [
            'business_type' => PartnerBusinessTypes::BUSINESS,
            'vat_type' => fake()->randomElement([
                PartnerVATTypes::LOCAL,
                PartnerVATTypes::EU,
                PartnerVATTypes::NONEU,
            ]),
        ]);
    }

    /**
     * Configure the factory for EU partner.
     */
    public function euPartner(): static
    {
        $euCountries = [
            TaxResidencyCountries::DE, TaxResidencyCountries::FR, TaxResidencyCountries::IT,
            TaxResidencyCountries::ES, TaxResidencyCountries::NL, TaxResidencyCountries::BE,
            TaxResidencyCountries::AT, TaxResidencyCountries::CZ, TaxResidencyCountries::SK,
        ];

        return $this->state(fn (array $attributes) => [
            'vat_type' => PartnerVATTypes::EU,
            'tax_residency_country' => fake()->randomElement($euCountries),
            'country_id' => fake()->randomElement(['DE', 'FR', 'IT', 'ES', 'NL', 'BE', 'AT', 'CZ', 'SK']),
            'vat_id' => $this->generateEuVatId(),
        ]);
    }

    /**
     * Configure the factory for non-EU partner.
     */
    public function nonEuPartner(): static
    {
        return $this->state(fn (array $attributes) => [
            'vat_type' => PartnerVATTypes::NONEU,
            'country_id' => fake()->randomElement(['US', 'GB', 'CH', 'NO', 'JP', 'CA']),
            'vat_id' => null, // Non-EU partners might not have VAT ID
        ]);
    }

    /**
     * Configure the factory for inactive partner.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Configure the factory for supplier partner.
     */
    public function supplier(): static
    {
        return $this->state(function (array $attributes) {
            $supplierTypes = [
                'Dostawca materiałów budowlanych',
                'Hurtownia elektroniczna',
                'Producent mebli',
                'Dystrybutor części samochodowych',
                'Dostawca usług IT',
                'Firma transportowa',
                'Dostawca surowców',
            ];

            return [
                'name' => fake()->randomElement($supplierTypes) . ' ' . fake()->company(),
                'business_type' => PartnerBusinessTypes::BUSINESS,
                'vat_type' => PartnerVATTypes::LOCAL,
            ];
        });
    }

    /**
     * Configure the factory for customer partner.
     */
    public function customer(): static
    {
        return $this->state(function (array $attributes) {
            $isIndividual = fake()->boolean(40);

            if ($isIndividual) {
                $firstName = fake()->firstName();
                $lastName = fake()->lastName();
                return [
                    'name' => $firstName . ' ' . $lastName,
                    'short_name' => $firstName . ' ' . $lastName[0] . '.',
                    'business_type' => PartnerBusinessTypes::INDIVIDUAL,
                    'vat_type' => PartnerVATTypes::NOTVAT,
                    'website' => null,
                ];
            }

            return [
                'business_type' => PartnerBusinessTypes::BUSINESS,
                'vat_type' => fake()->randomElement([
                    PartnerVATTypes::LOCAL,
                    PartnerVATTypes::NOTVAT,
                ]),
            ];
        });
    }

    /**
     * Generate a short name from company name.
     */
    private function generateShortName(string $companyName): string
    {
        $firstWord = explode(' ', $companyName)[0];
        return substr($firstWord, 0, 20);
    }

    /**
     * Generate a realistic Polish VAT ID.
     */
    private function generatePolishVatId(): string
    {
        $digits = '';
        for ($i = 0; $i < 10; $i++) {
            $digits .= fake()->numberBetween(0, 9);
        }

        // Format as XXX-XXX-XX-XX
        return substr($digits, 0, 3) . '-' . substr($digits, 3, 3) . '-' .
               substr($digits, 6, 2) . '-' . substr($digits, 8, 2);
    }

    /**
     * Generate a realistic EU VAT ID.
     */
    private function generateEuVatId(): string
    {
        $countryPrefixes = ['DE', 'FR', 'IT', 'ES', 'NL', 'BE', 'AT', 'CZ', 'SK'];
        $prefix = fake()->randomElement($countryPrefixes);

        return $prefix . fake()->numerify('########');
    }

    /**
     * Generate a realistic Polish bank account number.
     */
    private function generatePolishBankAccount(): string
    {
        $digits = fake()->numerify('####################');

        // Format as XX XXXX XXXX XXXX XXXX XXXX XXXX
        return substr($digits, 0, 2) . ' ' .
               substr($digits, 2, 4) . ' ' .
               substr($digits, 6, 4) . ' ' .
               substr($digits, 10, 4) . ' ' .
               substr($digits, 14, 4) . ' ' .
               substr($digits, 18, 4) . ' ' .
               substr($digits, 22, 4);
    }
}
