<div class="table-caption">Zmienione produkty</div>
<table class="items" style="width:190mm;">
    <!-- Nagłówki tabeli -->
    <tr class="heading">
        <td style="width: 73mm;">Opis usługi</td>
        <td style="width: 13mm;"><PERSON><PERSON><PERSON><PERSON></td>
        <td style="width: 13mm;">j.m.</td>
        @if($record->vat_method === \App\Enums\TradeDocVatMethod::BASE_ON_NET)
            <td style="width: 23mm;">Cena netto</td>
        @else
            <td style="width: 23mm;"><PERSON>na brutto</td>
        @endif
        <td style="width: 13mm;">Rabat</td>
        <td style="width: 8mm;">VAT</td>
        @if($record->vat_method === \App\Enums\TradeDocVatMethod::BASE_ON_NET)
            <td style="width: 28mm;">Wart. netto</td>
        @else
            <td style="width: 28mm;">Wart. brutto</td>
        @endif
    </tr>
    @foreach($record->items as $item)
        @php
        if (isset($items[$item->uuid])) {
            $calc = new \Illuminate\Support\Fluent($items[$item->uuid]);
        } else {
            $calc = $item;
        }
        @endphp
        <tr class="item">
            <td>{{$calc->label}}</td>
            <td>{{rtrim(number_format( $calc->amount, 3, ',', ''),'0,')}}</td>
            <td>{{$calc->unit_type}}</td>
            @if($record->vat_method === \App\Enums\TradeDocVatMethod::BASE_ON_NET)
                <td>{{number_format($calc->net_unit_price, 2, ',', '')}}</td>
            @else
                <td>{{number_format($calc->gross_unit_price, 2, ',', '')}}</td>
            @endif
            <td>{{$calc->discount_value ?? $item->discount_value}}</td>
            <td>{{$item->vat_label}}</td>
            @if($record->vat_method === \App\Enums\TradeDocVatMethod::BASE_ON_NET)
                <td>{{number_format($calc->net_value, 2, ',', '')}}</td>
            @else
                <td>{{number_format($calc->gross_value, 2, ',', '')}}</td>
            @endif
        </tr>
        <tr class="item-diff">
            <td style="text-align: right;">Różnica: </td>
            <td>{{rtrim(number_format( $item->amount, 3, ',', ''),'0,')}}</td>
            <td>{{$item->unit_type}}</td>
            @if($record->vat_method === \App\Enums\TradeDocVatMethod::BASE_ON_NET)
                <td>{{number_format($item->net_unit_price, 2, ',', '')}}</td>
            @else
                <td>{{number_format($item->gross_unit_price, 2, ',', '')}}</td>
            @endif
            <td>{{$item->discount_value}}</td>
            <td>{{$item->vat_label}}</td>
            @if($record->vat_method === \App\Enums\TradeDocVatMethod::BASE_ON_NET)
                <td>{{number_format($item->net_value, 2, ',', '')}}</td>
            @else
                <td>{{number_format($item->gross_value, 2, ',', '')}}</td>
            @endif
        </tr>
    @endforeach
</table>
<div class="table-caption" style="margin-top:5mm;">Rozliczenie VAT</div>
<table class="items" style="width:190mm;margin-top:2mm;">
    <tr style="border-bottom: 1px solid black;" class="heading">
        <td style="text-align: center;"></td>
        <td style="text-align: center;">Różnica po korekcie</td>
    </tr>
    <tr>
        <td>
{{--            <table>--}}
{{--                <tr>--}}
{{--                    <td style="text-align: right;">--}}
{{--                        <strong>Wartość netto:</strong>--}}
{{--                    </td>--}}
{{--                    <td style="text-align: right;">--}}
{{--                        {{number_format($vat_source['summary']['net_amount'], 2, ',', '')}}--}}
{{--                    </td>--}}
{{--                </tr>--}}
{{--                @foreach($vat_source as $key => $value)--}}
{{--                    @if($key !== 'summary')--}}
{{--                        <tr class="total">--}}
{{--                            <td style="text-align: right;"><strong>VAT {{$key}}:</strong></td>--}}
{{--                            <td style="text-align: right;">{{number_format($value['vat_amount'], 2, ',', '')}}</td>--}}
{{--                        </tr>--}}
{{--                    @endif--}}
{{--                @endforeach--}}
{{--                <tr class="total">--}}
{{--                    <td style="text-align: right;"><strong>Wartość brutto:</strong></td>--}}
{{--                    <td style="text-align: right;">{{number_format($vat_source['summary']['gross_amount'], 2, ',', '')}}</td>--}}
{{--                </tr>--}}
{{--            </table>--}}
        </td>
        <td>
            <table class="vat-table">
                <tr>
                    <td style="text-align: right;">
                        <strong>Wartość netto:</strong>
                    </td>
                    <td style="text-align: right;">
                        {{number_format($vat['summary']['net_amount'], 2, ',', '')}}
                    </td>
                </tr>
                @foreach($vat as $key => $value)
                    @if($key !== 'summary')
                        <tr class="total">
                            <td style="text-align: right;"><strong>VAT {{$key}}:</strong></td>
                            <td style="text-align: right;">{{number_format($value['vat_amount'], 2, ',', '')}}</td>
                        </tr>
                    @endif
                @endforeach
                <tr class="total">
                    <td style="text-align: right;"><strong>Wartość brutto:</strong></td>
                    <td style="text-align: right;">{{number_format($vat['summary']['gross_amount'], 2, ',', '')}}</td>
                </tr>
            </table>
        </td>
    </tr>
</table>
