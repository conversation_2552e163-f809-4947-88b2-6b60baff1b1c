<?php
$issuer = $document->issuer_address;
$seller = $document->options['different_seller'] ? $document->seller_address : $document->issuer_address;
//    dd($document);
$buyer = $document->buyer_address;
$bank = $document->bank_data;
$vat = $document->vat;
$options = $document->options;
$invoiceConfig = tenant()->getInvoiceConfiguration();
?>
@extends('print.trade_docs.main')
@section('content')
    <table cellpadding="0" cellspacing="0" class="header">
        <!-- <PERSON><PERSON><PERSON> częś<PERSON> faktury -->
        <tr class="top-logo">
            <td colspan="4">
                <table>
                    <tr>
                        <td>
                            <h2>Faktura {{$record->full_doc_number}}</h2>
                            @if (filled($invoiceConfig->get('special_note')) && in_array('special_note', $invoiceConfig->get('templates')['v2']['selected_fields']))
                                <div style="margin-top: 2mm;">
                                    <div>
                                        <span class="alert">{{$invoiceConfig->get('special_note')}}</span><br>
                                    </div>
                                </div>
                            @endif
                        </td>
                        <td style="text-align: right;">
                            Data wystawienia: {{$record->issued_at->format('Y-m-d')}}<br>
                            Data sprzedaży: {{$record->sells_date->format('Y-m-d')}}<br>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr class="top">
            <td colspan="4">
                <table>
                    <tr>
                        <td>
                            <strong>Sprzedawca:</strong><br>
                            {!! nl2br($seller['name']) !!}<br>
                            {{$seller['address']}}<br>
                            {{$seller['postcode']}} {{$seller['city']}}<br>
                            @if ($seller['vat_id'])
                                NIP: {{$seller['vat_id']}} <br>
                            @endif
                            BDO: 1212545<br>
                        </td>
                        <td style="text-align: right;">
                            Termin płatności: {{$record->payment_due_date->format('Y-m-d')}}<br>
                            Forma płatności: {{$record->payment_type->label()}}<br>
                            Waluta: {{$record->currency}}<br>
                            Bank: {{$bank['bank_name'] ?? 'brak'}}<br>
                            Konto: {{$bank['bank_account'] ?? 'brak'}}<br>
                            @if($bank['bank_swift'] ?? false)
                                SWIFT: {{$bank['bank_swift']}}<br>
                            @endif
                            @if($bank['bank_iban'] ?? false)
                                IBAN: {{$bank['bank_iban']}}<br>
                            @endif
                        </td>
                    </tr>
                </table>
            </td>
        </tr>

        <!-- Informacje o nabywcy -->
        <tr class="information">
            <td colspan="4">
                <table>
                    <tr>
                        <td>
                            <strong>Nabywca:</strong><br>
                            {!! nl2br($buyer['name']) !!}<br>
                            {{$buyer['address']}}<br>
                            {{$buyer['postcode']}} {{$buyer['city']}}<br>
                            @if ($buyer['vat_id'])
                                NIP: {{$buyer['vat_id']}} <br>
                            @endif
                        </td>
                        <td style="text-align: right;">
                            {{--                            @if($document->getOption('reverse_charge', false))--}}
                            {{--                                <strong>{{__('app.trade_docs._.reverse_charge')}}</strong><br>--}}
                            {{--                            @endif--}}
                            {{--                            @if($document->getOption('mpp', false))--}}
                            {{--                                <strong>{{__('app.trade_docs._.mpp')}}</strong><br>--}}
                            {{--                            @endif--}}
                            {{--                            <strong>VAT </strong>{{$record->vat_method->label()}} <br>--}}
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    @include('print.trade_docs.v2.parts.faup_items')
    @include('print.trade_docs.v2.parts.summary')
    @include('print.trade_docs.v2.parts.footer')
@endsection
