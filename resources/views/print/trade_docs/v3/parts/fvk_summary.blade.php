<table class="prefooter" style="width:19cm;margin-left:auto;margin-right: auto;padding:0;" border="0" cellpadding="0"
       cellspacing="0">
    <tr>
        <td style="width:12cm;text-align: left;">
            <table class="opisf">
                <tr>
                    <td style="">Waluta:&nbsp;<b>{{$record->currency}}</b></td>
                </tr>
                <tr>
                    <td style="">Forma płatności: <b>{{$record->payment_type->label()}}</b></td>
                </tr>
                <tr>
                    <td style="">Termin zapłaty: <b>{{$record->payment_due_date->format('Y-m-d')}}</b></td>
                </tr>
                <tr>
                    <td>
                        @if($vat['summary']['gross_amount'] > 0)
                            Do zapłaty:
                        @else
                           Do zwrotu:
                        @endif
                            <strong>{{number_format(abs($vat['summary']['gross_amount'] ?? 0), 2, ',', '')}} {{$record->currency}}</strong>
                    </td>
                </tr>
                <tr>
                    <td>
                        Przyczyna korekty:
                        <strong> {!! $record->notes !!} </strong>
                    </td>
                </tr>
            </table>
        </td>
        <td style="width:7cm;text-align: left;padding:0px;" valign="top" cellpadding="0" cellspacing='0'>

            <table width="100%" class="vat" cellpadding="2" cellspacing="0" align="right">
                <tr>
                    <td colspan="4">
                        W tym:
                    </td>
                </tr>
                <tr>
                    <td>St. Vat %</td>
                    <td>Netto</td>
                    <td>VAT</td>
                    <td>Brutto</td>
                </tr>
                @foreach($vat as $k => $v)
                    @if($k !== 'summary')
                        <tr>
                            <td>{{ $k }}</td>
                            <td>{{ number_format($v['net_amount'] ?? 0, 2, ',', ' ') }}</td>
                            <td>{{ number_format($v['vat_amount'] ?? 0, 2, ',', ' ') }}</td>
                            <td>{{ number_format($v['gross_amount'] ?? 0, 2, ',', ' ') }}</td>
                        </tr>
                    @endif
                @endforeach
            </table>

        </td>
    </tr>
</table>
