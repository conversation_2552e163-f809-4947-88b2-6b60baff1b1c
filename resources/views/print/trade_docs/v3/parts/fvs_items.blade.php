<table style="width:100%;margin-left:auto;margin-right: auto;margin-bottom:0.5cm;" cellspacing="0"
       class='pozycje'>
    <tr>
        <th style="width: 4mm;"></th>
        <th style="width: 74mm;">Nazwa produktu</th>
        <th style="width: 9mm;">j.m.</th>
        <th style="width: 16mm;"><PERSON><PERSON><PERSON><PERSON></th>
        @if($record->vat_method === \App\Enums\TradeDocVatMethod::BASE_ON_NET)
            <th style="width: 16mm;">Cena jedn. netto</th>
        @else
            <th style="width: 16mm;">Cena jedn. brutto</th>
        @endif
        <th style="width: 10mm;">Rabat %</th>
        <th style="width: 8mm;">VAT %</th>
        <th style="width: 16mm;">Wartość VAT</th>
        @if($record->vat_method === \App\Enums\TradeDocVatMethod::BASE_ON_NET)
        <th style="width: 16mm;">Wartoś<PERSON> netto</th>
        @else
        <th style="width: 16mm;">Wartość brutto</th>
        @endif
    </tr>
    @php
    $counter = 1;
    @endphp
    @foreach($record->items as $item)
        <tr>
            <td>{{ $counter++ }}</td>
            <td>{{$item->label}}</td>
            <td>{{$item->unit_type}}</td>
            <td style='text-align:right;'>{{rtrim(number_format( $item->amount, 3, ',', ''),'0,')}}</td>
            <td style='text-align:right;'>
                @if($record->vat_method === \App\Enums\TradeDocVatMethod::BASE_ON_NET)
                    {{number_format($item->net_unit_price, 2, ',', '')}}
                    @else
                    {{number_format($item->gross_unit_price, 2, ',', '')}}
                @endif
            </td>
            <td style='text-align:right;'>{{$item->discount_value}}</td>
            <td style='text-align:right;'>{{$item->vat_label}}</td>
            <td style='text-align:right;'>
                {{number_format($item->vat_value, 2, ',', '')}}
            </td>
            <td style='text-align:right;'>
                @if($record->vat_method === \App\Enums\TradeDocVatMethod::BASE_ON_NET)
                    {{number_format($item->net_value, 2, ',', '')}}
                    @else
                    {{number_format($item->gross_value, 2, ',', '')}}
                @endif
            </td>
        </tr>
    @endforeach
</table>
