<?php

use App\Http\Controllers\PaymentWebhookController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
//
//Route::prefix('webhook/payments')->group(function () {
//    Route::post('/stripe', [PaymentWebhookController::class, 'handleStripe']);
//    Route::post('/payu', [PaymentWebhookController::class, 'handlePayU']);
//    Route::post('/przelewy24', [PaymentWebhookController::class, 'handlePrzelewy24']);
//});

Route::post('/webhook/payments/{provider}', [PaymentWebhookController::class, 'handlePayment'])
->name('payments-webhook');
